import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Plant } from 'api/models/plants';
import { RootState } from 'app/store';
import { sortBy, sortSizeName } from 'utils/sort';

export interface PlantsState {
  plants: Plant[];
}

const initialState: PlantsState = {
  plants: []
};

export const plantsSlice = createSlice({
  name: 'plants',
  initialState,
  reducers: {
    setPlants(state, action: PayloadAction<Plant[]>) {
      state.plants = action.payload;
    },
    moveItem(state, action: PayloadAction<{ existingItem: Plant, movingItem: Plant }>) {
      const { movingItem, existingItem } = action.payload;

      console.log("moving item", existingItem, movingItem);

      if (existingItem.stickingSortOrder == null) {
        existingItem.stickingSortOrder = 0;
        movingItem.stickingSortOrder = 1;
        //move the rest up by 1
        state.plants = state.plants.map(p => {
          if (p._id === existingItem._id) {
            return existingItem;
          } else if (p._id === movingItem._id) {
            return movingItem;
          } else if (p.stickingSortOrder != null) {
            p.stickingSortOrder++;
          }
          return p;
        });
      } else {
        // set the moved item to the existing item's order
        movingItem.stickingSortOrder = existingItem.stickingSortOrder;
        // move the rest up or down by 1
        state.plants = state.plants.map(p => {
          if (p._id === existingItem._id) {
            return existingItem;
          } else if (p._id === movingItem._id) {
            return movingItem;
          } else if (p.stickingSortOrder != null) {
            if (p.stickingSortOrder > existingItem.stickingSortOrder) {
              p.stickingSortOrder--;
            } else {
              p.stickingSortOrder++;
            }
          }
          return p;
        });
      }
    }
  }
});

export const { setPlants, moveItem } = plantsSlice.actions;

export const selectPlants = (state: RootState) => state.plants.plants.map(p => ({...p})).sort(sortPlant);

export default plantsSlice.reducer;

const sortByCrop = sortBy('crop');
const sortByStickingSortOrder = sortBy('stickingSortOrder');

function sortPlant(a: Plant, b: Plant) {
  return sortByStickingSortOrder(a, b) || sortByCrop(a, b) || sortSizeName(a.size, b.size);
}